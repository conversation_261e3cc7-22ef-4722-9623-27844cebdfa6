<?xml version="1.0" encoding="UTF-8"?><subLeg xmlns="http://www.xml.gov.hk/schemas/hklm/1.0" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.xml.gov.hk/schemas/hklm/1.0 https://www.elegislation.gov.hk/schemas/hklm.xsd" xml:lang="en"><meta><docName>Cap. 99 sub. leg. G</docName><docType>cap</docType><docNumber>99G</docNumber><docStatus>In effect</docStatus><dc:identifier>/hk/cap99G!en</dc:identifier><dc:date>2020-11-19</dc:date><dc:subject>legislation</dc:subject><dc:language>en</dc:language><dc:publisher>DoJ</dc:publisher><dc:rights>the Government of the Hong Kong Special Administrative Region</dc:rights></meta><main xml:lang="en" xsi:schemaLocation="http://www.xml.gov.hk/schemas/hklm/1.0 https://www.elegislation.gov.hk/schemas/hklm.xsd"><docTitle id="ID_1438403578682_003" name="shortTitle" reason="inEffect" status="operational" temporalId="shortTitle" startPeriod="2020-11-19">Pension
            Benefits
            (Prescribed
            Ages)
            (Senior
            Rank
            and
            Rank
            and
            File
            Grades)
            (Immigration
            Department)
            Order</docTitle><enactingFormula class="align_center">(<ref href="/hk/cap99/s10">Cap. 99,
                section
            10(3)(b)</ref>)<marker role="blank-line"></marker><marker role="blank-line"></marker></enactingFormula><commencementNote>[10 July 1987]</commencementNote><sourceNote>(Format changes—<ref href="/hk/2020/er6">E.R. 6 of 2020</ref>)</sourceNote><section id="ID_1438403578697_001" name="s1" reason="inEffect" role="paragraph" startPeriod="2020-11-19" status="operational" temporalId="s1"><num value="1">1.</num><heading>Citation</heading><content>This
            Order
            may be cited as the Pension Benefits (Prescribed Ages) (Senior Rank and Rank and File
            Grades) (Immigration Department) Order. </content><sourceNote>(<ref href="/hk/2020/er6">E.R. 6 of 2020</ref>)</sourceNote></section><section id="ID_1438403578697_003" name="s2" reason="inEffect" role="paragraph" startPeriod="2020-11-19" status="operational" temporalId="s2"><num value="2">2.</num><heading>Prescribed ages</heading><content>In relation to a rank or grade (being a rank or grade specified in the <ref>Schedule</ref> to the Ordinance) mentioned in column 1 of the <ref>Schedule</ref>, the age specified in column 2 thereof opposite such mention of the rank or grade, is hereby prescribed for the purposes of <ref>section 10(3)(b)</ref> of the Ordinance. </content></section><schedule id="ID_1438403578697_005" name="sch0" reason="inEffect" startPeriod="2020-11-19" status="operational" temporalId="sch0"><num value="0">Schedule</num><note class="align_right" role="crossReferences" type="inline">[<ref href="/hk/cap99G/s2">para.
                2</ref>]<marker role="blank-line"></marker></note><content>
            
            
            <table xmlns="http://www.w3.org/1999/xhtml" frame="void" width="100%">
                <col width="10%"></col>
                <col width="55%"></col>
                <col width="25%"></col>
                <col width="10%"></col>
                <tbody>
                    
                    
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    
                    
                    <tr>
                        <td></td>
                        <td class="align_center border_top_0.75 border_right_0.75 border_left_0.75">1</td>
                        <td class="align_center border_top_0.75 border_right_0.75">2</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td class="align_center cellpadding_bottom_4 border_left_0.75 border_bottom_0.75 border_right_0.75">Rank or grade</td>
                        <td class="align_center border_left_0.75 border_bottom_0.75 border_right_0.75">Age</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td class="border_left_0.75 border_right_0.75">
                            <p class="left_indent_9 first_line_indent_0">All senior ranks</p>
                        </td>
                        <td class="align_center border_right_0.75">55</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td class="border_left_0.75 border_right_0.75 border_bottom_0.75">
                            <p class="left_indent_9 first_line_indent_0">All rank and file
                                grades</p>
                        </td>
                        <td class="align_center border_left_0.75 border_right_0.75 border_bottom_0.75">55</td>
                        <td></td>
                    </tr>
                    
                    
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    
                    
                </tbody>
            </table>
            
            
            <marker role="blank-line"></marker>
            <marker role="blank-line"></marker>
        </content></schedule></main></subLeg>