import fitz  # PyMuPDF
import sys
import os

def pdf_to_text(pdf_path, output_txt_path=None):
    if not os.path.exists(pdf_path):
        print(f"File not found: {pdf_path}")
        return

    doc = fitz.open(pdf_path)
    text = ""
    for page_num, page in enumerate(doc):
        text += f"\n\n--- Page {page_num + 1} ---\n\n"
        text += page.get_text()

    if output_txt_path is None:
        output_txt_path = os.path.splitext(pdf_path)[0] + ".txt"

    with open(output_txt_path, "w", encoding="utf-8") as f:
        f.write(text)

    print(f"Text extracted and saved to: {output_txt_path}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python pdf_to_text.py <path-to-pdf>")
    else:
        pdf_to_text(sys.argv[1])
